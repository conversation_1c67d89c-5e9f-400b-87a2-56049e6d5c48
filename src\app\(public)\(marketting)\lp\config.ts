export const pageData = {
  // Header Banner
  headerBanner: {
    discountText: "40%",
    offerText: "OFF CUSTOM BLINDS AND FREE INSTALLATION",
  },

  // Header Navbar
  navbar: {
    contactButton: {
      text: "Contact US",
      href: "/contact",
    },
  },

  // Hero Section
  hero: {
    backgroundImage: "/backgrounds/lp.webp",
    specialOffer: {
      badge: "SPECIAL OFFER",
      title: "Upto 50% OFF Custom Blinds",
    },
    benefits: [
      "FREE Window Blinds Installation",
      "Discounted Motorization Upgrade",
    ],
    pricing: {
      originalPrice: "$250",
      discountedPrice: "$200",
    },
    description:
      "Mention this offer when you request an estimate (phone, form, and email) & Save!",
    ctaButton: {
      text: "Request A Free Estimate",
    },
    certificates: [
      {
        src: "/certificate1.png",
        alt: "Certificate1",
      },
      {
        src: "/certificate2.png",
        alt: "Certificate2",
      },
    ],
    formSection: {
      award: {
        src: "/award.png",
        alt: "Award",
      },
      title: "Request A Free Estimate",
      contactButton: {
        text: "Contact US",
      },
    },
  },
};
